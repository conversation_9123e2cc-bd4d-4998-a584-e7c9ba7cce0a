import getCosClient from '../utils/tencentClient';
import { COS_CONFIG } from '../utils/tencentEnv';
import { logger } from '../utils/logger';

// 导入并发任务执行工具函数
/**
 * 并发执行异步任务
 * @param {Array<Function>} tasks 任务函数数组，每个函数返回一个Promise
 * @param {number} maxConcurrent 最大并发数
 * @returns {Promise<void>}
 */
async function executeConcurrentTasks(tasks, maxConcurrent) {
  const executing = [];
  let completedCount = 0;

  for (const task of tasks) {
    const promise = task().then(() => {
      completedCount++;
      executing.splice(executing.indexOf(promise), 1);
    }).catch(error => {
      completedCount++;
      console.error(`任务失败: ${completedCount}/${tasks.length}`, error);
      executing.splice(executing.indexOf(promise), 1);
    });

    executing.push(promise);

    if (executing.length >= maxConcurrent) {
      await Promise.race(executing);
    }
  }

  await Promise.all(executing);
}

/**
 * 检查错误是否为COS认证相关错误
 * @param {Error} error - 错误对象
 * @returns {boolean} 是否为认证错误
 */
function isAuthenticationError(error) {
  if (!error) return false;

  const authErrorCodes = [
    'SignatureDoesNotMatch',
    'InvalidAccessKeyId',
    'AccessDenied',
    'RequestTimeTooSkewed',
    'AuthorizationHeaderMalformed',
    'InvalidToken',
    'TokenRefreshRequired',
    'Forbidden'
  ];

  const authErrorMessages = [
    'signature does not match',
    'access denied',
    'invalid access key',
    'authorization header malformed',
    'request time too skewed',
    'forbidden'
  ];

  const errorCode = error.code || error.Code || '';
  const errorMessage = (error.message || error.Message || '').toLowerCase();
  const statusCode = error.statusCode || error.StatusCode || 0;

  return authErrorCodes.includes(errorCode) ||
         authErrorMessages.some(msg => errorMessage.includes(msg)) ||
         statusCode === 403;
}

/**
 * 记录COS认证错误的详细信息
 * @param {Error} error - 错误对象
 * @param {string} operationName - 操作名称
 * @param {Object} requestInfo - 请求信息
 */
function logAuthenticationError(error, operationName, requestInfo = {}) {
  console.error(`[COS Auth Error] ${operationName} 认证失败:`);
  console.error(`[COS Auth Error] 错误代码:`, error.code || error.Code || 'Unknown');
  console.error(`[COS Auth Error] 错误消息:`, error.message || error.Message || 'Unknown');
  console.error(`[COS Auth Error] HTTP状态码:`, error.statusCode || error.StatusCode || 'Unknown');
  console.error(`[COS Auth Error] 请求ID:`, error.RequestId || error.requestId || 'Unknown');

  if (requestInfo.method) {
    console.error(`[COS Auth Error] 请求方法:`, requestInfo.method);
  }
  if (requestInfo.url) {
    console.error(`[COS Auth Error] 请求URL:`, requestInfo.url);
  }
  if (requestInfo.headers) {
    console.error(`[COS Auth Error] 请求头:`, requestInfo.headers);
  }

  // 检查是否在开发环境
  if (import.meta.env.DEV) {
    console.error(`[COS Auth Error] 开发环境提示: 请检查Vite代理配置是否正确转发认证头部`);
    console.error(`[COS Auth Error] 建议检查: vite.config.js中的/satworld-resource代理配置`);
  }
}

/**
 * 通用的异步函数重试辅助方法。
 * @async
 * @param {function(): Promise<any>} fn - 需要执行并可能重试的异步函数。
 * @param {number} [retries=3] - 最大重试次数。
 * @param {number} [delay=1000] - 初始重试延迟时间（毫秒）。
 * @param {string} [operationName='操作'] - 操作的名称，用于日志输出。
 * @returns {Promise<any>} 异步函数的执行结果。
 * @throws {Error} 如果所有重试均失败，则抛出最后一次尝试的错误。
 */
async function retry(fn, retries = 3, delay = 1000, operationName = '操作') {
  try {
    return await fn();
  } catch (error) {
    // 检查是否为认证错误
    if (isAuthenticationError(error)) {
      logAuthenticationError(error, operationName);
      // 认证错误通常不需要重试，直接抛出
      throw error;
    }

    if (retries <= 0) {
      logger.error(`${operationName}失败，已达最大重试次数。最终错误:`, error);
      throw error;
    }
    console.warn(`${operationName}发生错误，将在 ${delay}ms 后重试 (${retries} 次剩余)。错误:`, error.message);
    await new Promise(resolve => setTimeout(resolve, delay));
    // 指数退避增加下一次延迟，但可以增加一个最大延迟上限避免过长等待
    return retry(fn, retries - 1, Math.min(delay * 2, 30000), operationName); // 例如，最大延迟30秒
  }
}

// 预签名 URL 缓存
const urlCache = new Map();

/**
 * @typedef {object} ListedObject
 * @property {string} key - 对象键 (完整路径)
 * @property {string} lastModified - 最后修改时间
 * @property {string} etag - 对象的ETag
 * @property {number} size - 对象大小 (字节)
 * @property {string} storageClass - 存储类别
 */

/**
 * @typedef {object} ListedPrefix
 * @property {string} prefix - 公共前缀 (模拟文件夹)
 */

/**
 * @typedef {object} ListObjectsResult
 * @property {ListedObject[]} files - 文件列表
 * @property {ListedPrefix[]} folders - 文件夹列表 (公共前缀)
 * @property {string|null} nextMarker - 用于下一页查询的标记，如果没有更多则为null
 * @property {string} currentPrefix - 当前查询的前缀
 */

/**
 * @typedef {object} CopyStatus
 * @property {string} sourceKey - 源对象的键
 * @property {string} destinationKey - 目标对象的键
 * @property {'success' | 'failure'} status - 复制状态
 * @property {string} [error] - 如果复制失败，则为错误信息
 */

/**
 * @typedef {object} DeleteError
 * @property {string} Key - 删除失败的对象的Key
 * @property {string} Code - 错误码
 * @property {string} Message - 错误信息
 */

/**
 * @typedef {object} BatchDeleteResult
 * @property {string[]} deleted - 成功删除的对象Key列表。
 * @property {DeleteError[]} errors - 删除失败的对象列表及其错误信息。
 * @property {string} prefix - 执行删除操作的原始前缀。
 * @property {number} totalAttempted - 尝试删除的对象总数。
 * @property {'success' | 'partial_success' | 'failure'} overallStatus - 整体删除状态。
 * @property {string} [message] - 关于整体操作的附加消息。
 */

/**
 * @typedef {object} UploadStatus
 * @property {string} fileName - 原始文件名
 * @property {string} objectKey - COS对象键
 * @property {'success' | 'failure'} status - 上传状态
 * @property {string} [error] - 如果上传失败，则为错误信息
 * @property {string} [location] - 如果上传成功，则为COS位置
 * @property {number} fileSize - 文件大小（字节）
 * @property {string} [relativePath] - 相对路径（用于目录上传）
 */

/**
 * @typedef {object} BatchUploadResult
 * @property {UploadStatus[]} results - 各个文件的上传结果数组
 * @property {number} totalFiles - 处理的文件总数
 * @property {number} successCount - 成功上传的文件数
 * @property {number} failureCount - 上传失败的文件数
 * @property {'success' | 'partial_success' | 'failure'} overallStatus - 整体上传状态
 * @property {string} [message] - 关于整体操作的附加消息
 * @property {string[]} createdFolders - 创建的文件夹占位符列表
 */

/**
 * @typedef {object} DownloadStatus
 * @property {string} objectKey - COS对象键
 * @property {string} fileName - 文件名
 * @property {'success' | 'failure'} status - 下载状态
 * @property {string} [error] - 如果下载失败，则为错误信息
 * @property {string} [downloadUrl] - 如果生成成功，则为下载URL
 * @property {number} [fileSize] - 文件大小（字节）
 */

/**
 * @typedef {object} BatchDownloadResult
 * @property {DownloadStatus[]} results - 各个文件的下载结果数组
 * @property {number} totalFiles - 处理的文件总数
 * @property {number} successCount - 成功生成下载链接的文件数
 * @property {number} failureCount - 生成下载链接失败的文件数
 * @property {'success' | 'partial_success' | 'failure'} overallStatus - 整体下载状态
 * @property {string} [message] - 关于整体操作的附加消息
 */

/**
 * @typedef {object} BatchCopyResult
 * @property {CopyStatus[]} results - 各个对象的复制结果数组
 * @property {number} totalObjects - 处理的对象总数
 * @property {number} successCount - 成功复制的对象数
 * @property {number} failureCount - 复制失败的对象数
 * @property {'success' | 'partial_success' | 'failure'} overallStatus - 整体复制状态
 * @property {string} [message] - 关于整体操作的附加消息
 * @property {string[]} processedDirectories - 处理的目录列表
 */

/**
 * 列出COS中指定前缀（目录）下的对象（文件和文件夹）。
 * @async
 * @param {string} [prefix=''] - 要列出的对象的前缀（例如 'images/' 或 'documents/subfolder/'）。默认为空字符串，表示根目录。
 * @param {string} [marker=null] - 上一次列举操作返回的 `NextMarker` 值，用于获取下一页结果。首次调用时省略或传入null。
 * @param {number} [maxKeys=1000] - 单次请求返回的最大对象数量，默认为1000。
 * @returns {Promise<ListObjectsResult>} 包含文件、文件夹和下一个分页标记的对象。
 * @throws {Error} 当COS SDK调用失败时抛出错误。
 */
export async function listObjectsInDirectory(prefix = 'website_public', marker = null, maxKeys = 1000, signal = null) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;
  const region = COS_CONFIG.region;

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    logger.error(errorMsg);
    throw new Error(errorMsg);
  }

  const params = {
    Bucket: bucketName,
    Region: region,
    Prefix: prefix,
    MaxKeys: maxKeys,
    Delimiter: '/', // 使用 '/' 作为分隔符来模拟文件夹结构
  };

  if (marker) {
    params.Marker = marker;
  }

  try {
    // 检查是否被取消
    if (signal && signal.aborted) {
      throw new Error('操作被取消');
    }

    // // 只在首次列出时打印日志
    // if (!marker) {
    //   logger.debug(`[listObjects] 列出目录: ${prefix || 'website_public'}`);
    //   logger.debug(`[listObjects] 请求参数:`, JSON.stringify(params, null, 2));
    // }

    // 使用重试机制调用COS API
    const result = await retry(
      () => cosClient.getBucket(params),
      3,
      1000,
      `列出目录 ${prefix || 'website_public'}`
    );

    // logger.debug(`[listObjects] API响应:`, result);

    // 解析响应数据
    const files = [];
    const folders = [];

    // 处理文件列表
    if (result.Contents && Array.isArray(result.Contents)) {
      result.Contents.forEach(item => {
        files.push({
          key: item.Key,
          lastModified: item.LastModified,
          etag: item.ETag,
          size: parseInt(item.Size, 10),
          storageClass: item.StorageClass || 'STANDARD'
        });
      });
    }

    // 处理文件夹列表（公共前缀）
    if (result.CommonPrefixes && Array.isArray(result.CommonPrefixes)) {
      result.CommonPrefixes.forEach(item => {
        folders.push({
          prefix: item.Prefix
        });
      });
    }

    // 确定下一页标记
    const nextMarker = result.IsTruncated === 'true' ? result.NextMarker || null : null;

    const listResult = {
      files,
      folders,
      nextMarker,
      currentPrefix: prefix
    };

    console.log(`[listObjects] 解析结果: 文件 ${files.length} 个，文件夹 ${folders.length} 个，下一页标记: ${nextMarker}`);

    return listResult;
  } catch (error) {
    console.error(`[listObjects] 列出目录失败: ${prefix}`, error);
    throw error;
  }
}

/**
 * 上传对象到COS。
 * @async
 * @param {string} objectKey - 对象在COS上的完整键 (例如 'documents/report.docx')。
 * @param {File} fileObject - 要上传的文件对象。
 * @param {function(number): void} [onProgressCallback] - 上传进度回调函数，接收进度百分比 (0-100)。
 * @param {object} [options={}] - 上传选项。
 * @param {number} [options.partSize=8*1024*1024] - 分片大小，默认为8MB。
 * @param {number} [options.taskNum=3] - 并发上传的分片任务数，默认为3。
 * @param {boolean} [options.enableCheckpoint=true] - 是否启用断点续传功能，默认为true。
 * @returns {Promise<{key: string, status: 'success' | 'failure', error?: string, location?: string}>} 操作结果，成功时包含文件的 COS Location。
 * @throws {Error} 如果COS客户端初始化失败或Bucket名称未配置，则抛出错误或返回失败状态。
 */
export async function uploadObject(objectKey, fileObject, onProgressCallback, options = {}) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;
  const region = COS_CONFIG.region;

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    return { key: objectKey, status: 'failure', error: errorMsg };
  }

  if (!objectKey) {
    const errorMsg = '目标对象键 (objectKey) 不能为空。';
    console.error(errorMsg);
    return { key: objectKey, status: 'failure', error: errorMsg };
  }

  if (!fileObject || typeof fileObject.name === 'undefined' || typeof fileObject.size === 'undefined') {
    const errorMsg = '提供的 fileObject 无效。它应该是一个HTML File对象。';
    console.error(errorMsg);
    return { key: objectKey, status: 'failure', error: errorMsg };
  }

  console.log(`开始上传文件: '${objectKey}'，大小: ${fileObject.size} 字节`);

  try {
    const params = {
      Bucket: bucketName,
      Region: region,
      Key: objectKey,
      Body: fileObject,
      onProgress: function(progressData) {
        if (onProgressCallback && typeof onProgressCallback === 'function') {
          const percent = Math.round(progressData.percent * 100);
          onProgressCallback(percent);
        }
      }
    };

    // 使用 uploadFile 方法进行分段上传，它更适合大文件并支持进度
    const result = await cosClient.uploadFile(params);

    if (result.statusCode === 200) {
      logger.success(`成功上传文件: '${objectKey}'，Location: ${result.Location}`);
      return {
        key: objectKey,
        status: 'success',
        location: result.Location, // 返回文件的访问URL
      };
    } else {
      const errorDetail = `COS上传文件错误: ${result.statusCode} - ${result.statusMessage}`;
      logger.error(`上传文件 '${objectKey}' 失败. 原因: ${errorDetail}`, result);
      return { key: objectKey, status: 'failure', error: errorDetail };
    }
  } catch (error) {
    // 检查是否为认证错误并记录详细信息
    if (isAuthenticationError(error)) {
      logAuthenticationError(error, `上传文件 '${objectKey}'`, {
        method: 'PUT',
        bucket: bucketName,
        key: objectKey,
        fileSize: fileObject.size
      });
    }

    const errorDetail = `上传文件 '${objectKey}' 时发生异常: ${error.message}`;
    console.error(errorDetail, error);

    // 提供更详细的错误信息
    let detailedError = errorDetail;
    if (error.code || error.Code) {
      detailedError += ` (错误代码: ${error.code || error.Code})`;
    }
    if (error.statusCode || error.StatusCode) {
      detailedError += ` (HTTP状态码: ${error.statusCode || error.StatusCode})`;
    }

    return { key: objectKey, status: 'failure', error: detailedError };
  }
}

/**
 * 获取COS对象的预签名下载URL。
 * 此函数会首先检查内部缓存中是否有有效的预签名URL，以减少不必要的API调用。
 * @async
 * @param {string} objectKey - 要获取下载URL的对象的完整键 (例如 'data/report.pdf')。
 * @param {number} [expiresInSeconds=3600] - 预签名URL的有效时间（秒），默认为1小时 (3600秒)。最大值通常由COS服务限制。
 * @param {boolean} [forceGenerate=false] - 是否强制重新生成URL，忽略缓存。默认为false。
 * @returns {Promise<{key: string, url: string | null, status: 'success' | 'failure', error?: string}>} 包含预签名URL或错误信息的对象。
 * @throws {Error} 如果COS客户端初始化失败或Bucket名称未配置，则抛出错误或返回失败状态。
 */
export async function getPresignedUrlForDownload(objectKey, expiresInSeconds = 3600, forceGenerate = false) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;
  const region = COS_CONFIG.region;

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    return { key: objectKey, url: null, status: 'failure', error: errorMsg };
  }

  if (!objectKey) {
    const errorMsg = '对象键 (objectKey) 不能为空。';
    console.error(errorMsg);
    return { key: objectKey, url: null, status: 'failure', error: errorMsg };
  }

  // 检查缓存（如果不强制生成）
  if (!forceGenerate && urlCache.has(objectKey)) {
    const cachedEntry = urlCache.get(objectKey);
    if (cachedEntry.expiresAt > Date.now()) {
      console.log(`从缓存中获取预签名URL: '${objectKey}'`);
      return {
        key: objectKey,
        url: cachedEntry.url,
        status: 'success'
      };
    } else {
      // 缓存已过期，删除
      urlCache.delete(objectKey);
      console.log(`预签名URL缓存已过期，已删除: '${objectKey}'`);
    }
  }

  // 验证 expiresInSeconds 是否在合理范围内
  if (expiresInSeconds <= 0 || expiresInSeconds > 604800) { // 例如，最大7天
    console.warn(`预签名URL的有效时间 ${expiresInSeconds} 秒无效或过长，将使用默认值 3600 秒。`);
    expiresInSeconds = 3600;
  }

  console.log(`准备为对象 '${objectKey}' 生成预签名下载URL，有效期: ${expiresInSeconds} 秒`);

  try {
    const params = {
      Bucket: bucketName,
      Region: region,
      Key: objectKey,
      Method: 'GET',
      Expires: expiresInSeconds,
    };

    const createUrlCall = () => cosClient.getObjectUrl(params);
    const result = await retry(createUrlCall, 3, 1000, `为 '${objectKey}' 生成预签名URL`);

    if (result) {
      let signedUrl = result;

      // COS SDK已经根据环境配置了正确的域名（开发环境使用代理，生产环境使用COS域名）
      // 如果需要额外的URL处理（如CDN加速），可以在这里进行

      console.log(`成功为 '${objectKey}' 生成预签名URL: ${signedUrl}`);

      const expiresAt = Date.now() + (expiresInSeconds * 1000) - 5000; // 提前5秒标记过期
      urlCache.set(objectKey, { url: signedUrl, expiresAt });

      // 设置定时器在URL过期时从缓存中移除
      setTimeout(() => {
        const currentCacheEntry = urlCache.get(objectKey);
        if (currentCacheEntry && currentCacheEntry.url === signedUrl && currentCacheEntry.expiresAt <= Date.now()) {
          urlCache.delete(objectKey);
          console.log(`预签名URL缓存已为 '${objectKey}' 清理 (自动过期)`);
        }
      }, expiresInSeconds * 1000); // 使用原始有效期作为setTimeout延迟

      return {
        key: objectKey,
        url: signedUrl,
        status: 'success'
      };
    } else {
      const errorDetail = `COS未返回有效的预签名URL`;
      console.error(`为 '${objectKey}' 生成预签名URL失败. 原因: ${errorDetail}`);
      return { key: objectKey, url: null, status: 'failure', error: errorDetail };
    }
  } catch (error) {
    const errorDetail = `为 '${objectKey}' 生成预签名URL时发生异常: ${error.message}`;
    console.error(errorDetail, error);
    return { key: objectKey, url: null, status: 'failure', error: errorDetail };
  }
}

/**
 * 删除COS中的单个对象。
 * @async
 * @param {string} objectKey - 要删除的对象的完整键 (例如 'documents/report.docx')。
 * @returns {Promise<{key: string, status: 'success' | 'failure', error?: string}>} 操作结果。
 * @throws {Error} 如果COS客户端初始化失败或Bucket名称未配置，则抛出错误或返回失败状态。
 */
export async function deleteObject(objectKey) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;
  const region = COS_CONFIG.region;

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    return { key: objectKey, status: 'failure', error: errorMsg };
  }

  if (!objectKey) {
    const errorMsg = '对象键 (objectKey) 不能为空。';
    console.error(errorMsg);
    return { key: objectKey, status: 'failure', error: errorMsg };
  }

  console.log(`准备删除对象: '${objectKey}'`);

  try {
    const params = {
      Bucket: bucketName,
      Region: region,
      Key: objectKey
    };

    const result = await retry(
      () => cosClient.deleteObject(params),
      3,
      1000,
      `删除对象 '${objectKey}'`
    );

    if (result.statusCode === 204) {
      console.log(`成功删除对象: '${objectKey}'`);

      // 从预签名URL缓存中移除
      if (urlCache.has(objectKey)) {
        urlCache.delete(objectKey);
        console.log(`已从预签名URL缓存中移除: '${objectKey}'`);
      }

      return {
        key: objectKey,
        status: 'success'
      };
    } else {
      const errorDetail = `COS删除对象错误: ${result.statusCode} - ${result.statusMessage}`;
      console.error(`删除对象 '${objectKey}' 失败. 原因: ${errorDetail}`, result);
      return { key: objectKey, status: 'failure', error: errorDetail };
    }
  } catch (error) {
    const errorDetail = `删除对象 '${objectKey}' 时发生异常: ${error.message}`;
    console.error(errorDetail, error);
    return { key: objectKey, status: 'failure', error: errorDetail };
  }
}

/**
 * 在COS中创建文件夹占位符。
 * 由于COS是对象存储，不存在真正的文件夹概念，此函数通过创建一个以 '/' 结尾的空对象来模拟文件夹。
 * @async
 * @param {string} folderKey - 文件夹在COS上的完整路径，必须以 '/' 结尾 (例如 'myfolder/subfolder/')。
 * @returns {Promise<{key: string, status: 'success' | 'failure', error?: string}>} 操作结果。
 * @throws {Error} 如果COS客户端初始化失败、Bucket名称未配置或folderKey无效，则抛出错误或返回失败状态。
 */
export async function createFolderPlaceholder(folderKey) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;
  const region = COS_CONFIG.region;

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    return { key: folderKey, status: 'failure', error: errorMsg };
  }

  if (!folderKey || !folderKey.endsWith('/')) {
    const errorMsg = '文件夹键 (folderKey) 必须以 "/" 结尾。';
    console.error(errorMsg);
    return { key: folderKey, status: 'failure', error: errorMsg };
  }

  console.log(`准备创建文件夹占位符: '${folderKey}'`);

  try {
    const params = {
      Bucket: bucketName,
      Region: region,
      Key: folderKey,
      Body: '', // 空内容
    };

    const result = await retry(
      () => cosClient.putObject(params),
      3,
      1000,
      `创建文件夹占位符 '${folderKey}'`
    );

    if (result.statusCode === 200) {
      console.log(`成功创建文件夹占位符: '${folderKey}'`);
      return {
        key: folderKey,
        status: 'success'
      };
    } else {
      const errorDetail = `COS创建文件夹占位符错误: ${result.statusCode} - ${result.statusMessage}`;
      console.error(`创建文件夹占位符 '${folderKey}' 失败. 原因: ${errorDetail}`, result);
      return { key: folderKey, status: 'failure', error: errorDetail };
    }
  } catch (error) {
    const errorDetail = `创建文件夹占位符 '${folderKey}' 时发生异常: ${error.message}`;
    console.error(errorDetail, error);
    return { key: folderKey, status: 'failure', error: errorDetail };
  }
}

/**
 * 批量删除COS中的文件。
 * @async
 * @param {string[]} keys - 要删除的对象键数组。
 * @param {number} [batchSize=1000] - 每批处理的对象数量，默认1000。
 * @param {function(number, number): void} [onProgress] - 进度回调函数，接收 (已处理数量, 总数量) 参数。
 * @returns {Promise<BatchDeleteResult>} 包含已删除对象、删除错误和整体状态的对象。
 * @throws {Error} 如果COS客户端初始化失败或Bucket名称未配置，则可能抛出错误或返回特定结果。
 */
export async function batchDeleteFiles(keys, batchSize = 1000, onProgress = null) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;
  const region = COS_CONFIG.region;

  const resultSummary = {
    deleted: [],
    errors: [],
    prefix: '', // 批量删除没有特定前缀
    totalAttempted: keys.length,
    overallStatus: 'success'
  };

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    resultSummary.overallStatus = 'failure';
    resultSummary.message = errorMsg;
    return resultSummary;
  }

  if (!keys || keys.length === 0) {
    console.log('没有要删除的对象键。');
    return resultSummary;
  }

  console.log(`开始批量删除 ${keys.length} 个对象，批次大小: ${batchSize}`);

  // 分批处理
  for (let i = 0; i < keys.length; i += batchSize) {
    const batch = keys.slice(i, i + batchSize);
    console.log(`处理批次 ${Math.floor(i / batchSize) + 1}，包含 ${batch.length} 个对象`);

    try {
      const params = {
        Bucket: bucketName,
        Region: region,
        Objects: batch.map(key => ({ Key: key }))
      };

      const result = await retry(
        () => cosClient.deleteMultipleObject(params),
        3,
        1000,
        `批量删除第 ${Math.floor(i / batchSize) + 1} 批`
      );

      // 处理删除结果
      if (result.Deleted && Array.isArray(result.Deleted)) {
        result.Deleted.forEach(item => {
          resultSummary.deleted.push(item.Key);
          // 从预签名URL缓存中移除
          if (urlCache.has(item.Key)) {
            urlCache.delete(item.Key);
          }
        });
      }

      if (result.Error && Array.isArray(result.Error)) {
        result.Error.forEach(item => {
          resultSummary.errors.push({
            Key: item.Key,
            Code: item.Code,
            Message: item.Message
          });
        });
      }

    } catch (error) {
      console.error(`批次 ${Math.floor(i / batchSize) + 1} 删除失败:`, error);
      // 将整个批次标记为失败
      batch.forEach(key => {
        resultSummary.errors.push({
          Key: key,
          Code: 'BatchError',
          Message: error.message
        });
      });
    }

    // 调用进度回调
    if (onProgress && typeof onProgress === 'function') {
      const processed = Math.min(i + batchSize, keys.length);
      onProgress(processed, keys.length);
    }
  }

  // 确定整体状态
  if (resultSummary.errors.length === 0) {
    resultSummary.overallStatus = 'success';
  } else if (resultSummary.deleted.length > 0) {
    resultSummary.overallStatus = 'partial_success';
  } else {
    resultSummary.overallStatus = 'failure';
  }

  console.log(`批量删除完成。成功: ${resultSummary.deleted.length}，失败: ${resultSummary.errors.length}`);

  return resultSummary;
}

/**
 * 根据前缀删除COS中的对象。
 * @async
 * @param {string} prefix - 要删除对象的前缀 (例如 'logs/old/' 或 'temp_files/')。必须以 '/' 结尾。
 * @returns {Promise<BatchDeleteResult>} 包含已删除对象、删除错误和整体状态的对象。
 * @throws {Error} 如果COS客户端初始化失败、Bucket名称未配置或前缀无效，则抛出错误或返回特定结果。
 */
export async function deleteObjectsByPrefix(prefix) {
  logger.operation(`[deleteObjectsByPrefix] 开始批量删除，前缀: "${prefix}"`);

  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;
  const region = COS_CONFIG.region;

  const resultSummary = {
    deleted: [],
    errors: [],
    prefix: prefix,
    totalAttempted: 0,
    overallStatus: 'success'
  };

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    resultSummary.overallStatus = 'failure';
    resultSummary.message = errorMsg;
    return resultSummary;
  }

  if (!prefix) {
    const errorMsg = '前缀 (prefix) 不能为空。';
    console.error(errorMsg);
    resultSummary.overallStatus = 'failure';
    resultSummary.message = errorMsg;
    return resultSummary;
  }

  try {
    // 首先列出所有匹配前缀的对象，包括目录占位符
    logger.debug(`[deleteObjectsByPrefix] 列出前缀为 "${prefix}" 的所有对象`);

    let allObjects = [];
    let marker = null;
    let hasMore = true;

    // 使用不带分隔符的方式列出所有对象，包括目录占位符
    while (hasMore) {
      const params = {
        Bucket: bucketName,
        Region: COS_CONFIG.region,
        Prefix: prefix,
        MaxKeys: 1000
      };

      if (marker) {
        params.Marker = marker;
      }

      const result = await retry(
        () => getCosClient().getBucket(params),
        3,
        1000,
        `列出所有对象（包括目录占位符）: ${prefix}`
      );

      if (result.Contents && Array.isArray(result.Contents)) {
        result.Contents.forEach(item => {
          allObjects.push(item.Key);
        });
      }

      marker = result.IsTruncated === 'true' ? result.NextMarker || null : null;
      hasMore = marker !== null;
    }

    resultSummary.totalAttempted = allObjects.length;

    if (allObjects.length === 0) {
      logger.info(`[deleteObjectsByPrefix] 没有找到匹配前缀 "${prefix}" 的对象`);
      resultSummary.message = `没有找到匹配前缀 "${prefix}" 的对象`;
      return resultSummary;
    }

    logger.operation(`[deleteObjectsByPrefix] 找到 ${allObjects.length} 个对象需要删除（包括目录占位符）`);

    // 使用批量删除函数
    const deleteResult = await batchDeleteFiles(allObjects, 1000, (processed, total) => {
      logger.progress(`[deleteObjectsByPrefix] 删除进度: ${processed}/${total}`);
    });

    // 合并结果
    resultSummary.deleted = deleteResult.deleted;
    resultSummary.errors = deleteResult.errors;
    resultSummary.overallStatus = deleteResult.overallStatus;

    logger.success(`[deleteObjectsByPrefix] 完成。成功删除: ${resultSummary.deleted.length}，失败: ${resultSummary.errors.length}`);

    return resultSummary;

  } catch (error) {
    logger.error(`[deleteObjectsByPrefix] 删除前缀 "${prefix}" 时发生异常:`, error);
    resultSummary.overallStatus = 'failure';
    resultSummary.message = error.message;
    return resultSummary;
  }
}

/**
 * 复制COS中的单个对象。
 * @async
 * @param {string} sourceKey - 源对象的完整键 (例如 'folder/file.txt')。
 * @param {string} destinationKey - 目标对象的完整键 (例如 'newfolder/newfile.txt')。
 * @returns {Promise<CopyStatus>} 包含复制状态的对象。
 * @throws {Error} 如果COS客户端初始化失败或Bucket名称未配置，则抛出错误。
 */
export async function copySingleObject(sourceKey, destinationKey) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;
  const region = COS_CONFIG.region;

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    return { sourceKey, destinationKey, status: 'failure', error: errorMsg };
  }

  if (!sourceKey || !destinationKey) {
    const errorMsg = '源键和目标键都不能为空。';
    console.error(errorMsg);
    return { sourceKey, destinationKey, status: 'failure', error: errorMsg };
  }

  console.log(`准备复制对象: '${sourceKey}' -> '${destinationKey}'`);

  try {
    const params = {
      Bucket: bucketName,
      Region: region,
      Key: destinationKey,
      CopySource: `${bucketName}.cos.${region}.myqcloud.com/${sourceKey}`
    };

    const result = await retry(
      () => cosClient.putObjectCopy(params),
      3,
      1000,
      `复制对象 '${sourceKey}' 到 '${destinationKey}'`
    );

    if (result.statusCode === 200) {
      console.log(`成功复制对象: '${sourceKey}' -> '${destinationKey}'`);
      return {
        sourceKey,
        destinationKey,
        status: 'success'
      };
    } else {
      const errorDetail = `COS复制对象错误: ${result.statusCode} - ${result.statusMessage}`;
      console.error(`复制对象失败. 原因: ${errorDetail}`, result);
      return { sourceKey, destinationKey, status: 'failure', error: errorDetail };
    }
  } catch (error) {
    const errorDetail = `复制对象时发生异常: ${error.message}`;
    console.error(errorDetail, error);
    return { sourceKey, destinationKey, status: 'failure', error: errorDetail };
  }
}

/**
 * 递归复制COS中指定目录下的所有对象到另一个目录。
 * @async
 * @param {string} sourceDirectory - 源目录的路径 (例如 'source/folder/' 或 'source/folder')。
 * @param {string} destinationDirectory - 目标目录的路径 (例如 'destination/folder/' 或 'destination/folder')。
 * @param {Set<string>} [existingTargetKeys=new Set()] - 可选参数，包含目标目录中已存在的对象键的Set，用于增量复制。
 * @returns {Promise<CopyStatus[]>} 一个包含每个对象复制状态的数组。
 * @throws {Error} 如果COS客户端初始化失败或Bucket名称未配置，则抛出错误。
 */
export async function batchCopyObjectsRecursive(sourceDirectory, destinationDirectory, existingTargetKeys = new Set(), signal = null) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  // 确保目录路径以 '/' 结尾
  const normalizedSourceDir = sourceDirectory.endsWith('/') ? sourceDirectory : sourceDirectory + '/';
  const normalizedDestDir = destinationDirectory.endsWith('/') ? destinationDirectory : destinationDirectory + '/';

  console.log(`开始递归复制: '${normalizedSourceDir}' -> '${normalizedDestDir}'`);

  const copyResults = [];

  try {
    // 列出源目录下的所有对象，包括文件和目录占位符
    let allObjects = [];
    let marker = null;
    let hasMore = true;

    while (hasMore) {
      // 检查是否被取消
      if (signal && signal.aborted) {
        throw new Error('操作被取消');
      }

      const listResult = await listObjectsInDirectory(normalizedSourceDir, marker, 1000);

      // 收集文件
      if (listResult.files && listResult.files.length > 0) {
        allObjects = allObjects.concat(listResult.files);
      }

      marker = listResult.nextMarker;
      hasMore = marker !== null;
    }

    // 现在获取所有对象（包括目录占位符）通过不使用分隔符
    marker = null;
    hasMore = true;
    const allObjectsWithPlaceholders = [];

    while (hasMore) {
      // 检查是否被取消
      if (signal && signal.aborted) {
        throw new Error('操作被取消');
      }

      // 直接调用COS API，不使用分隔符来获取所有对象包括目录占位符
      const params = {
        Bucket: bucketName,
        Region: COS_CONFIG.region,
        Prefix: normalizedSourceDir,
        MaxKeys: 1000
      };

      if (marker) {
        params.Marker = marker;
      }

      const result = await retry(
        () => cosClient.getBucket(params),
        3,
        1000,
        `列出所有对象（包括目录占位符）: ${normalizedSourceDir}`
      );

      if (result.Contents && Array.isArray(result.Contents)) {
        result.Contents.forEach(item => {
          allObjectsWithPlaceholders.push({
            key: item.Key,
            lastModified: item.LastModified,
            etag: item.ETag,
            size: parseInt(item.Size, 10),
            storageClass: item.StorageClass || 'STANDARD'
          });
        });
      }

      marker = result.IsTruncated === 'true' ? result.NextMarker || null : null;
      hasMore = marker !== null;
    }

    console.log(`找到 ${allObjectsWithPlaceholders.length} 个对象需要复制（包括目录占位符）`);

    // 复制每个对象
    for (const obj of allObjectsWithPlaceholders) {
      // 检查是否被取消
      if (signal && signal.aborted) {
        throw new Error('操作被取消');
      }

      const sourceKey = obj.key;
      // 计算目标键：将源目录前缀替换为目标目录前缀
      const relativePath = sourceKey.substring(normalizedSourceDir.length);
      const destinationKey = normalizedDestDir + relativePath;

      // 如果目标已存在且在排除列表中，跳过
      if (existingTargetKeys.has(destinationKey)) {
        console.log(`跳过已存在的对象: '${destinationKey}'`);
        copyResults.push({
          sourceKey,
          destinationKey,
          status: 'success', // 视为成功，因为目标已存在
          skipped: true
        });
        continue;
      }

      // 执行复制
      const copyResult = await copySingleObject(sourceKey, destinationKey);
      copyResults.push(copyResult);
    }

    console.log(`递归复制完成。总计: ${copyResults.length}，成功: ${copyResults.filter(r => r.status === 'success').length}`);

    return copyResults;

  } catch (error) {
    console.error(`递归复制失败: '${normalizedSourceDir}' -> '${normalizedDestDir}'`, error);
    throw error;
  }
}

/**
 * 批量上传文件和目录到COS。
 * 支持上传单个文件列表或包含目录结构的文件列表。
 * @async
 * @param {File[]|FileList} files - 要上传的文件数组或FileList对象。
 * @param {string} [targetDirectory=''] - 目标目录路径，默认为根目录。
 * @param {object} [options={}] - 上传选项。
 * @param {number} [options.maxConcurrent=3] - 最大并发上传数，默认为3。
 * @param {number} [options.partSize=8*1024*1024] - 分片大小，默认为8MB。
 * @param {number} [options.taskNum=3] - 并发上传的分片任务数，默认为3。
 * @param {boolean} [options.enableCheckpoint=true] - 是否启用断点续传功能，默认为true。
 * @param {boolean} [options.createFolderPlaceholders=true] - 是否为空目录创建占位符，默认为true。
 * @param {AbortSignal} [options.signal] - 用于取消操作的信号。
 * @param {function(number, number, string): void} [onProgressCallback] - 进度回调函数，接收 (已完成数量, 总数量, 当前文件名) 参数。
 * @returns {Promise<BatchUploadResult>} 包含所有文件上传结果的对象。
 * @throws {Error} 如果COS客户端初始化失败或参数无效，则抛出错误。
 */
export async function batchUpload(files, targetDirectory = '', options = {}, onProgressCallback = null) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  // 参数验证
  if (!files || files.length === 0) {
    const errorMsg = '文件列表不能为空。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  // 默认选项
  const {
    maxConcurrent = 3,
    partSize = 8 * 1024 * 1024,
    taskNum = 3,
    enableCheckpoint = true,
    createFolderPlaceholders = true,
    signal = null
  } = options;

  // 标准化目标目录路径
  const normalizedTargetDir = targetDirectory ?
    (targetDirectory.endsWith('/') ? targetDirectory : targetDirectory + '/') : '';

  console.log(`[batchUpload] 开始批量上传 ${files.length} 个文件到目录: ${normalizedTargetDir || '根目录'}`);

  const result = {
    results: [],
    totalFiles: files.length,
    successCount: 0,
    failureCount: 0,
    overallStatus: 'success',
    createdFolders: []
  };

  try {
    // 检查是否被取消
    if (signal && signal.aborted) {
      throw new Error('操作被取消');
    }

    // 转换FileList为数组
    const fileArray = Array.from(files);

    // 分析文件结构，识别目录和文件
    const fileStructure = new Map(); // 目录路径 -> 文件列表
    const allDirectories = new Set();

    fileArray.forEach(file => {
      let relativePath = '';
      let fileName = file.name;

      // 检查是否有webkitRelativePath（目录上传）
      if (file.webkitRelativePath) {
        relativePath = file.webkitRelativePath;
        const pathParts = relativePath.split('/');
        fileName = pathParts[pathParts.length - 1];

        // 收集所有目录路径
        for (let i = 1; i < pathParts.length; i++) {
          const dirPath = pathParts.slice(0, i).join('/') + '/';
          allDirectories.add(dirPath);
        }
      }

      const fullObjectKey = normalizedTargetDir + (relativePath || fileName);

      result.results.push({
        fileName: fileName,
        objectKey: fullObjectKey,
        status: 'pending',
        fileSize: file.size,
        relativePath: relativePath
      });
    });

    console.log(`[batchUpload] 识别到 ${allDirectories.size} 个目录需要创建占位符`);

    // 创建目录占位符
    if (createFolderPlaceholders && allDirectories.size > 0) {
      const folderTasks = Array.from(allDirectories).map(dirPath => async () => {
        if (signal && signal.aborted) {
          throw new Error('操作被取消');
        }

        const folderKey = normalizedTargetDir + dirPath;
        try {
          const folderResult = await createFolderPlaceholder(folderKey);
          if (folderResult.status === 'success') {
            result.createdFolders.push(folderKey);
            console.log(`[batchUpload] 成功创建文件夹占位符: ${folderKey}`);
          }
        } catch (error) {
          console.warn(`[batchUpload] 创建文件夹占位符失败: ${folderKey}`, error);
          // 文件夹创建失败不影响文件上传
        }
      });

      // 并发创建文件夹占位符
      await executeConcurrentTasks(folderTasks, Math.min(maxConcurrent, 5));
    }

    // 上传文件
    let completedCount = 0;
    const uploadTasks = fileArray.map((file, index) => async () => {
      if (signal && signal.aborted) {
        throw new Error('操作被取消');
      }

      const resultItem = result.results[index];
      const objectKey = resultItem.objectKey;

      try {
        console.log(`[batchUpload] 开始上传文件: ${file.name} -> ${objectKey}`);

        const uploadResult = await uploadObject(objectKey, file, (progress) => {
          // 单个文件的进度回调可以在这里处理
        }, {
          partSize,
          taskNum,
          enableCheckpoint
        });

        resultItem.status = uploadResult.status;
        if (uploadResult.status === 'success') {
          resultItem.location = uploadResult.location;
          result.successCount++;
        } else {
          resultItem.error = uploadResult.error;
          result.failureCount++;
        }

      } catch (error) {
        resultItem.status = 'failure';
        resultItem.error = error.message;
        result.failureCount++;
        console.error(`[batchUpload] 上传文件失败: ${file.name}`, error);
      }

      completedCount++;

      // 调用进度回调
      if (onProgressCallback && typeof onProgressCallback === 'function') {
        onProgressCallback(completedCount, result.totalFiles, file.name);
      }
    });

    // 并发执行上传任务
    await executeConcurrentTasks(uploadTasks, maxConcurrent);

    // 确定整体状态
    if (result.failureCount === 0) {
      result.overallStatus = 'success';
      result.message = `成功上传 ${result.successCount} 个文件`;
    } else if (result.successCount > 0) {
      result.overallStatus = 'partial_success';
      result.message = `部分成功：${result.successCount} 个成功，${result.failureCount} 个失败`;
    } else {
      result.overallStatus = 'failure';
      result.message = `所有文件上传失败`;
    }

    console.log(`[batchUpload] 批量上传完成。成功: ${result.successCount}，失败: ${result.failureCount}`);

    return result;

  } catch (error) {
    console.error(`[batchUpload] 批量上传过程中发生异常:`, error);
    result.overallStatus = 'failure';
    result.message = error.message;
    return result;
  }
}

/**
 * 获取COS对象的文件内容作为Blob。
 * 此函数会生成预签名URL并使用fetch获取文件内容。
 * @async
 * @param {string} objectKey - 要获取的对象的完整键 (例如 'data/report.pdf')。
 * @param {AbortSignal} [signal] - 用于取消操作的信号。
 * @param {number} [expiresInSeconds=3600] - 预签名URL的有效时间（秒），默认为1小时。
 * @returns {Promise<{key: string, blob: Blob | null, status: 'success' | 'failure', error?: string}>} 包含文件Blob或错误信息的对象。
 * @throws {Error} 如果COS客户端初始化失败或Bucket名称未配置，则抛出错误或返回失败状态。
 */
export async function fetchFileAsBlob(objectKey, signal = null, expiresInSeconds = 3600) {
  if (!objectKey) {
    const errorMsg = '对象键 (objectKey) 不能为空。';
    console.error(errorMsg);
    return { key: objectKey, blob: null, status: 'failure', error: errorMsg };
  }

  console.log(`[fetchFileAsBlob] 开始获取文件: ${objectKey}`);

  try {
    // 检查是否被取消
    if (signal && signal.aborted) {
      throw new Error('操作被取消');
    }

    // 首先获取预签名下载URL
    const urlResult = await getPresignedUrlForDownload(objectKey, expiresInSeconds);

    if (urlResult.status !== 'success' || !urlResult.url) {
      const errorMsg = urlResult.error || '获取预签名URL失败';
      console.error(`[fetchFileAsBlob] 获取预签名URL失败: ${objectKey} - ${errorMsg}`);
      return { key: objectKey, blob: null, status: 'failure', error: errorMsg };
    }

    // 再次检查是否被取消
    if (signal && signal.aborted) {
      throw new Error('操作被取消');
    }

    console.log(`[fetchFileAsBlob] 开始下载文件内容: ${objectKey}`);

    // 使用fetch获取文件内容
    const fetchOptions = {};
    if (signal) {
      fetchOptions.signal = signal;
    }

    const response = await fetch(urlResult.url, fetchOptions);

    if (!response.ok) {
      const errorMsg = `HTTP ${response.status}: ${response.statusText}`;
      console.error(`[fetchFileAsBlob] 下载文件失败: ${objectKey} - ${errorMsg}`);
      return { key: objectKey, blob: null, status: 'failure', error: errorMsg };
    }

    // 获取文件内容作为Blob
    const blob = await response.blob();

    console.log(`[fetchFileAsBlob] 文件下载成功: ${objectKey}, 大小: ${blob.size} 字节`);

    return {
      key: objectKey,
      blob: blob,
      status: 'success'
    };

  } catch (error) {
    if (error.name === 'AbortError' || error.message === '操作被取消') {
      console.log(`[fetchFileAsBlob] 下载被取消: ${objectKey}`);
      return { key: objectKey, blob: null, status: 'failure', error: '操作被取消' };
    }

    const errorDetail = `获取文件 '${objectKey}' 时发生异常: ${error.message}`;
    console.error(`[fetchFileAsBlob] ${errorDetail}`, error);
    return { key: objectKey, blob: null, status: 'failure', error: errorDetail };
  }
}

/**
 * 批量下载COS中的文件。
 * 在浏览器环境中，此函数会生成预签名下载URL并触发浏览器下载。
 * @async
 * @param {string[]|object[]} objectKeys - 要下载的对象键数组，或包含key和fileName属性的对象数组。
 * @param {object} [options={}] - 下载选项。
 * @param {number} [options.maxConcurrent=5] - 最大并发处理数，默认为5。
 * @param {number} [options.expiresInSeconds=3600] - 预签名URL的有效时间（秒），默认为1小时。
 * @param {boolean} [options.autoDownload=true] - 是否自动触发浏览器下载，默认为true。
 * @param {AbortSignal} [options.signal] - 用于取消操作的信号。
 * @param {function(number, number, string): void} [onProgressCallback] - 进度回调函数，接收 (已完成数量, 总数量, 当前文件名) 参数。
 * @returns {Promise<BatchDownloadResult>} 包含所有文件下载结果的对象。
 * @throws {Error} 如果COS客户端初始化失败或参数无效，则抛出错误。
 */
export async function batchDownload(objectKeys, options = {}, onProgressCallback = null) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  // 参数验证
  if (!objectKeys || objectKeys.length === 0) {
    const errorMsg = '对象键列表不能为空。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  // 默认选项
  const {
    maxConcurrent = 5,
    expiresInSeconds = 3600,
    autoDownload = true,
    signal = null
  } = options;

  console.log(`[batchDownload] 开始批量下载 ${objectKeys.length} 个文件`);

  const result = {
    results: [],
    totalFiles: objectKeys.length,
    successCount: 0,
    failureCount: 0,
    overallStatus: 'success'
  };

  try {
    // 检查是否被取消
    if (signal && signal.aborted) {
      throw new Error('操作被取消');
    }

    // 标准化对象键列表
    const normalizedKeys = objectKeys.map(item => {
      if (typeof item === 'string') {
        return {
          objectKey: item,
          fileName: item.split('/').pop() || item
        };
      } else if (typeof item === 'object' && item.key) {
        return {
          objectKey: item.key,
          fileName: item.fileName || item.name || item.key.split('/').pop() || item.key
        };
      } else {
        throw new Error(`无效的对象键格式: ${JSON.stringify(item)}`);
      }
    });

    // 初始化结果数组
    normalizedKeys.forEach(({ objectKey, fileName }) => {
      result.results.push({
        objectKey,
        fileName,
        status: 'pending'
      });
    });

    let completedCount = 0;

    // 创建下载任务
    const downloadTasks = normalizedKeys.map((keyInfo, index) => async () => {
      if (signal && signal.aborted) {
        throw new Error('操作被取消');
      }

      const { objectKey, fileName } = keyInfo;
      const resultItem = result.results[index];

      try {
        console.log(`[batchDownload] 生成下载链接: ${objectKey}`);

        // 生成预签名下载URL
        const urlResult = await getPresignedUrlForDownload(objectKey, expiresInSeconds);

        if (urlResult.status === 'success' && urlResult.url) {
          resultItem.status = 'success';
          resultItem.downloadUrl = urlResult.url;
          result.successCount++;

          // 如果启用自动下载，触发浏览器下载
          if (autoDownload) {
            try {
              const link = document.createElement('a');
              link.href = urlResult.url;
              link.download = fileName;
              link.style.display = 'none';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              console.log(`[batchDownload] 已触发下载: ${fileName}`);
            } catch (downloadError) {
              console.warn(`[batchDownload] 触发下载失败: ${fileName}`, downloadError);
              // 下载触发失败不影响URL生成成功的状态
            }
          }

        } else {
          resultItem.status = 'failure';
          resultItem.error = urlResult.error || '生成下载链接失败';
          result.failureCount++;
        }

      } catch (error) {
        resultItem.status = 'failure';
        resultItem.error = error.message;
        result.failureCount++;
        console.error(`[batchDownload] 处理文件失败: ${objectKey}`, error);
      }

      completedCount++;

      // 调用进度回调
      if (onProgressCallback && typeof onProgressCallback === 'function') {
        onProgressCallback(completedCount, result.totalFiles, fileName);
      }
    });

    // 并发执行下载任务
    await executeConcurrentTasks(downloadTasks, maxConcurrent);

    // 确定整体状态
    if (result.failureCount === 0) {
      result.overallStatus = 'success';
      result.message = `成功处理 ${result.successCount} 个文件的下载`;
    } else if (result.successCount > 0) {
      result.overallStatus = 'partial_success';
      result.message = `部分成功：${result.successCount} 个成功，${result.failureCount} 个失败`;
    } else {
      result.overallStatus = 'failure';
      result.message = `所有文件下载处理失败`;
    }

    console.log(`[batchDownload] 批量下载完成。成功: ${result.successCount}，失败: ${result.failureCount}`);

    return result;

  } catch (error) {
    console.error(`[batchDownload] 批量下载过程中发生异常:`, error);
    result.overallStatus = 'failure';
    result.message = error.message;
    return result;
  }
}

/**
 * 递归复制COS中的文件和目录到指定目标位置。
 * 支持复制单个文件、多个文件、单个目录或多个目录到同一目标位置。
 * @async
 * @param {string|string[]} sourcePaths - 源路径，可以是单个路径字符串或路径数组。支持文件和目录路径。
 * @param {string} destinationPath - 目标路径，必须是目录路径。
 * @param {object} [options={}] - 复制选项。
 * @param {number} [options.maxConcurrent=5] - 最大并发复制数，默认为5。
 * @param {boolean} [options.skipExisting=false] - 是否跳过已存在的文件，默认为false（覆盖）。
 * @param {boolean} [options.preserveStructure=true] - 是否保持目录结构，默认为true。
 * @param {AbortSignal} [options.signal] - 用于取消操作的信号。
 * @param {function(number, number, string): void} [onProgressCallback] - 进度回调函数，接收 (已完成数量, 总数量, 当前项目名) 参数。
 * @returns {Promise<BatchCopyResult>} 包含所有复制操作结果的对象。
 * @throws {Error} 如果COS客户端初始化失败或参数无效，则抛出错误。
 */
export async function recursiveCopy(sourcePaths, destinationPath, options = {}, onProgressCallback = null) {
  const cosClient = getCosClient();
  const bucketName = COS_CONFIG.bucket;

  if (!bucketName) {
    const errorMsg = 'COS Bucket 名称未在 tencentEnv.js 中配置。请检查 COS_CONFIG.bucket。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  // 参数验证
  if (!sourcePaths || (Array.isArray(sourcePaths) && sourcePaths.length === 0)) {
    const errorMsg = '源路径不能为空。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  if (!destinationPath) {
    const errorMsg = '目标路径不能为空。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  // 默认选项
  const {
    maxConcurrent = 5,
    skipExisting = false,
    preserveStructure = true,
    signal = null
  } = options;

  // 标准化源路径为数组
  const sourcePathArray = Array.isArray(sourcePaths) ? sourcePaths : [sourcePaths];

  // 标准化目标路径
  const normalizedDestPath = destinationPath.endsWith('/') ? destinationPath : destinationPath + '/';

  console.log(`[recursiveCopy] 开始递归复制 ${sourcePathArray.length} 个源路径到: ${normalizedDestPath}`);

  const result = {
    results: [],
    totalObjects: 0,
    successCount: 0,
    failureCount: 0,
    overallStatus: 'success',
    processedDirectories: []
  };

  try {
    // 检查是否被取消
    if (signal && signal.aborted) {
      throw new Error('操作被取消');
    }

    // 收集所有需要复制的对象
    const allCopyTasks = [];

    for (const sourcePath of sourcePathArray) {
      if (signal && signal.aborted) {
        throw new Error('操作被取消');
      }

      const normalizedSourcePath = sourcePath.endsWith('/') ? sourcePath : sourcePath + '/';

      console.log(`[recursiveCopy] 分析源路径: ${sourcePath}`);

      try {
        // 检查源路径是文件还是目录
        // 先尝试作为目录列出内容
        const listResult = await listObjectsInDirectory(normalizedSourcePath, null, 1000, signal);

        if (listResult.files.length > 0 || listResult.folders.length > 0) {
          // 这是一个目录，递归收集所有对象
          console.log(`[recursiveCopy] 检测到目录: ${sourcePath}，包含 ${listResult.files.length} 个文件和 ${listResult.folders.length} 个子目录`);

          result.processedDirectories.push(sourcePath);

          // 收集目录中的所有对象
          let marker = null;
          let hasMore = true;

          while (hasMore) {
            if (signal && signal.aborted) {
              throw new Error('操作被取消');
            }

            const pageResult = await listObjectsInDirectory(normalizedSourcePath, marker, 1000, signal);

            // 添加文件到复制任务
            pageResult.files.forEach(file => {
              const sourceKey = file.key;
              let destinationKey;

              if (preserveStructure) {
                // 保持目录结构：计算相对路径
                const relativePath = sourceKey.substring(normalizedSourcePath.length);
                const sourceBaseName = normalizedSourcePath.replace(/\/$/, '').split('/').pop();
                destinationKey = normalizedDestPath + sourceBaseName + '/' + relativePath;
              } else {
                // 不保持结构：直接放在目标目录下
                const fileName = sourceKey.split('/').pop();
                destinationKey = normalizedDestPath + fileName;
              }

              allCopyTasks.push({
                sourceKey,
                destinationKey,
                type: 'file',
                size: file.size
              });
            });

            marker = pageResult.nextMarker;
            hasMore = marker !== null;
          }

        } else {
          // 可能是单个文件，尝试直接复制
          const sourceKey = sourcePath;
          const fileName = sourceKey.split('/').pop();
          const destinationKey = normalizedDestPath + fileName;

          console.log(`[recursiveCopy] 检测到文件: ${sourcePath}`);

          allCopyTasks.push({
            sourceKey,
            destinationKey,
            type: 'file',
            size: 0 // 单个文件大小未知
          });
        }

      } catch (error) {
        console.error(`[recursiveCopy] 分析源路径失败: ${sourcePath}`, error);
        // 继续处理其他路径
      }
    }

    result.totalObjects = allCopyTasks.length;
    console.log(`[recursiveCopy] 总共需要复制 ${result.totalObjects} 个对象`);

    if (result.totalObjects === 0) {
      result.message = '没有找到需要复制的对象';
      return result;
    }

    // 如果需要跳过已存在的文件，先检查目标目录
    let existingTargetKeys = new Set();
    if (skipExisting) {
      console.log(`[recursiveCopy] 检查目标目录中已存在的文件...`);
      try {
        let marker = null;
        let hasMore = true;

        while (hasMore) {
          if (signal && signal.aborted) {
            throw new Error('操作被取消');
          }

          const listResult = await listObjectsInDirectory(normalizedDestPath, marker, 1000, signal);
          listResult.files.forEach(file => existingTargetKeys.add(file.key));
          marker = listResult.nextMarker;
          hasMore = marker !== null;
        }

        console.log(`[recursiveCopy] 目标目录中已存在 ${existingTargetKeys.size} 个文件`);
      } catch (error) {
        console.warn(`[recursiveCopy] 检查目标目录失败，将继续复制:`, error);
      }
    }

    // 执行复制任务
    let completedCount = 0;
    const copyTasks = allCopyTasks.map(task => async () => {
      if (signal && signal.aborted) {
        throw new Error('操作被取消');
      }

      const { sourceKey, destinationKey, type } = task;

      // 如果需要跳过已存在的文件
      if (skipExisting && existingTargetKeys.has(destinationKey)) {
        console.log(`[recursiveCopy] 跳过已存在的文件: ${destinationKey}`);
        result.results.push({
          sourceKey,
          destinationKey,
          status: 'success',
          skipped: true
        });
        result.successCount++;
        completedCount++;

        if (onProgressCallback && typeof onProgressCallback === 'function') {
          onProgressCallback(completedCount, result.totalObjects, sourceKey.split('/').pop());
        }
        return;
      }

      try {
        const copyResult = await copySingleObject(sourceKey, destinationKey);
        result.results.push(copyResult);

        if (copyResult.status === 'success') {
          result.successCount++;
        } else {
          result.failureCount++;
        }

      } catch (error) {
        result.results.push({
          sourceKey,
          destinationKey,
          status: 'failure',
          error: error.message
        });
        result.failureCount++;
        console.error(`[recursiveCopy] 复制对象失败: ${sourceKey}`, error);
      }

      completedCount++;

      // 调用进度回调
      if (onProgressCallback && typeof onProgressCallback === 'function') {
        onProgressCallback(completedCount, result.totalObjects, sourceKey.split('/').pop());
      }
    });

    // 并发执行复制任务
    await executeConcurrentTasks(copyTasks, maxConcurrent);

    // 确定整体状态
    if (result.failureCount === 0) {
      result.overallStatus = 'success';
      result.message = `成功复制 ${result.successCount} 个对象`;
    } else if (result.successCount > 0) {
      result.overallStatus = 'partial_success';
      result.message = `部分成功：${result.successCount} 个成功，${result.failureCount} 个失败`;
    } else {
      result.overallStatus = 'failure';
      result.message = `所有对象复制失败`;
    }

    console.log(`[recursiveCopy] 递归复制完成。成功: ${result.successCount}，失败: ${result.failureCount}`);

    return result;

  } catch (error) {
    console.error(`[recursiveCopy] 递归复制过程中发生异常:`, error);
    result.overallStatus = 'failure';
    result.message = error.message;
    return result;
  }
}
