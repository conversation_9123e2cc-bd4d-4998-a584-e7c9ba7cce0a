#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 安全的打印函数
safe_print() {
    echo -e "$1"
}

safe_print "${BLUE}🔗 腾讯云COS JSON文件上传工具${NC}"
safe_print "${BLUE}📡 使用与 Node.js 相同的 Vite 代理配置${NC}"
safe_print "${BLUE}🔄 只上传MD5发生变化的JSON文件${NC}"
safe_print "============================================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        safe_print "${RED}❌ 错误: 未找到Python${NC}"
        safe_print "${YELLOW}💡 请先安装Python 3.7或更高版本${NC}"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

safe_print "${GREEN}✅ 找到Python: $($PYTHON_CMD --version)${NC}"

# 设置虚拟环境目录
VENV_DIR="cos_upload_env"

# 检查虚拟环境是否存在
if [ ! -d "$VENV_DIR" ]; then
    safe_print "${YELLOW}📦 创建Python虚拟环境...${NC}"
    $PYTHON_CMD -m venv $VENV_DIR
    if [ $? -ne 0 ]; then
        safe_print "${RED}❌ 创建虚拟环境失败${NC}"
        exit 1
    fi
    safe_print "${GREEN}✅ 虚拟环境创建成功${NC}"
else
    safe_print "${GREEN}✅ 虚拟环境已存在${NC}"
fi

# 激活虚拟环境
safe_print "${YELLOW}🔌 激活虚拟环境...${NC}"
source $VENV_DIR/bin/activate
if [ $? -ne 0 ]; then
    safe_print "${RED}❌ 激活虚拟环境失败${NC}"
    exit 1
fi

# 检查腾讯云COS SDK
safe_print "${YELLOW}📦 检查腾讯云COS SDK...${NC}"
python -c "import qcloud_cos" 2>/dev/null
if [ $? -eq 0 ]; then
    safe_print "${GREEN}✅ 腾讯云COS SDK 已安装${NC}"
else
    safe_print "${YELLOW}⚠️  未找到腾讯云COS SDK，正在安装...${NC}"
    pip install cos-python-sdk-v5
    if [ $? -eq 0 ]; then
        safe_print "${GREEN}✅ 腾讯云COS SDK 安装成功${NC}"
    else
        safe_print "${RED}❌ 腾讯云COS SDK 安装失败${NC}"
        safe_print "${YELLOW}💡 提示：请检查网络连接或尝试使用sudo权限${NC}"
        exit 1
    fi
fi

# 检查requests库
python -c "import requests" 2>/dev/null
if [ $? -ne 0 ]; then
    safe_print "${YELLOW}📦 安装requests库...${NC}"
    pip install requests
fi

echo

# 检查命令行参数
MODE="upload"
INIT_MODE=false

case "$1" in
    --test|-t)
        MODE="test"
        ;;
    --init|-i)
        INIT_MODE=true
        ;;
    --help|-h)
        safe_print "${BLUE}使用说明:${NC}"
        safe_print "  ./upload_cos.sh           # 正常上传模式"
        safe_print "  ./upload_cos.sh --init    # 初始化MD5记录"
        safe_print "  ./upload_cos.sh --test    # 测试连接配置"
        safe_print "  ./upload_cos.sh --help    # 显示帮助信息"
        exit 0
        ;;
esac

if [ "$MODE" = "test" ]; then
    safe_print "${BLUE}🧪 运行连接测试模式${NC}"
    python test_cos_connection.py
    exit_code=$?
elif [ "$INIT_MODE" = true ]; then
    safe_print "${BLUE}🔄 MD5记录初始化模式${NC}"
    python upload_json_files_proxy.py --init
    exit_code=$?
else
    safe_print "${BLUE}🚀 正常上传模式（只上传变化的文件）${NC}"
    python upload_json_files_proxy.py
    exit_code=$?
fi

echo

if [ $exit_code -eq 0 ]; then
    safe_print "${GREEN}✅ 操作完成${NC}"
else
    safe_print "${RED}❌ 操作失败，错误代码: $exit_code${NC}"
fi

echo
safe_print "${BLUE}💡 使用说明:${NC}"
safe_print "   ./upload_cos.sh           # 正常上传模式"
safe_print "   ./upload_cos.sh --init    # 初始化MD5记录"
safe_print "   ./upload_cos.sh --test    # 测试连接配置"
safe_print "   ./upload_cos.sh --help    # 显示帮助信息"
echo

# 退出时保持虚拟环境激活状态，让用户可以继续使用
safe_print "${YELLOW}💡 虚拟环境仍处于激活状态，输入 'deactivate' 可退出${NC}"

exit $exit_code
