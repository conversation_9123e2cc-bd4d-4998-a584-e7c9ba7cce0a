import COS from 'cos-js-sdk-v5';
import { COS_CONFIG, validateCOSConfig } from './tencentEnv';

// 单例模式：缓存客户端实例
let cosClientInstance = null;
let isInitialized = false;

/**
 * @description 初始化并返回腾讯云COS客户端实例（单例模式）。
 *              使用Vite代理后的路径作为server地址。
 * @returns {COS} COS客户端实例。
 * @throws {Error} 如果SecretId或SecretKey未配置，则抛出错误。
 */
const getCosClient = () => {
  // 如果已经初始化过，直接返回缓存的实例
  if (cosClientInstance) {
    return cosClientInstance;
  }

  // 验证配置
  if (!validateCOSConfig()) {
    const errorMsg = '腾讯云COS的SecretId或SecretKey未正确配置，请检查 src/utils/tencentEnv.js 文件。';
    console.error(errorMsg);
    throw new Error(errorMsg);
  }

  // 从配置文件中获取SecretId和SecretKey
  const { secretId, secretKey, region } = COS_CONFIG;

  // 在开发环境中配置代理域名以避免CORS问题
  const isDevelopment = import.meta.env.DEV;

  // 在开发环境中，我们需要特殊处理：
  // 1. 签名计算使用真实的COS域名
  // 2. 实际请求通过代理发送
  let cosConfig = {};

  if (isDevelopment) {
    // 开发环境：使用代理域名，但通过自定义授权函数确保签名正确
    const proxyDomain = `${window.location.host}/satworld-resource`;
    const protocol = window.location.protocol;
    const realCOSDomain = `${COS_CONFIG.bucket}.cos.${region}.myqcloud.com`;

    cosConfig = {
      // 不直接提供SecretId和SecretKey，而是通过getAuthorization回调
      getAuthorization: function(options, callback) {
        // 临时修改options中的域名信息，确保签名基于真实COS域名计算
        const originalHost = options.Headers && options.Headers.Host;
        const originalUrl = options.Url;

        // 为签名计算设置正确的Host
        if (options.Headers) {
          options.Headers.Host = realCOSDomain;
        }

        // 如果URL包含代理域名，临时替换为真实域名进行签名计算
        if (options.Url && options.Url.includes(proxyDomain)) {
          options.Url = options.Url.replace(`${protocol}//${proxyDomain}`, `https://${realCOSDomain}`);
        }

        // console.log(`[COS Dev Auth] 为签名计算临时修改Host: ${realCOSDomain}`);
        // console.log(`[COS Dev Auth] 签名参数:`, {
        //   Method: options.Method,
        //   Pathname: options.Pathname,
        //   Host: options.Headers?.Host
        // });

        // 使用COS SDK的内置签名算法
        try {
          const authorization = COS.getAuthorization({
            SecretId: secretId,
            SecretKey: secretKey,
            Method: options.Method,
            Pathname: options.Pathname,
            Query: options.Query,
            Headers: options.Headers,
            Expires: 3600
          });

          // console.log(`[COS Dev Auth] 签名生成成功`);

          // 恢复原始值
          if (originalHost && options.Headers) {
            options.Headers.Host = originalHost;
          }
          if (originalUrl) {
            options.Url = originalUrl;
          }

          callback({
            Authorization: authorization
          });
        } catch (error) {
          console.error(`[COS Dev Auth] 签名生成失败:`, error);
          callback({ error });
        }
      },

      Protocol: protocol,
      Domain: proxyDomain
    };
  } else {
    // 生产环境：使用标准配置
    const proxyDomain = COS_CONFIG.domain || `${COS_CONFIG.bucket}.cos.${region}.myqcloud.com`;
    const protocol = COS_CONFIG.protocol;

    cosConfig = {
      SecretId: secretId,
      SecretKey: secretKey,
      Protocol: protocol,
      Domain: proxyDomain
    };
  }

  // 添加通用配置项到cosConfig
  cosConfig = {
    ...cosConfig,
    // 可选配置项
    FileParallelLimit: 3,    // 控制文件上传并发数
    ChunkParallelLimit: 8,   // 控制单个文件下分片上传并发数，在同园区上传可以设置较大的并发数
    ChunkSize: 1024 * 1024 * 8, // 控制分片大小，单位 B，在同园区上传可以设置较大的分片大小
    SliceSize: 1024 * 1024 * 8, // 使用分片上传的文件大小阈值，默认值1MB，小于等于该数值的文件使用简单上传，大于该数值的文件使用分片上传
    CopyChunkParallelLimit: 20, // 分片复制并发数
    CopyChunkSize: 1024 * 1024 * 64, // 分片复制时每片的大小
    CopySliceSize: 1024 * 1024 * 64, // 复制文件时启用分片复制的文件大小阈值
    ProgressInterval: 1000, // 上传进度的回调方法 onProgress 的回调频率，单位 ms ，默认值1000
    UploadCheckContentMd5: true, // 上传文件时校验 Content-MD5
    Timeout: 120000, // 超时时间，单位毫秒，默认为0，即不设置超时时间
    ForcePathStyle: false, // 强制使用后缀式模式发请求
    UseAccelerate: false, // 是否使用上传加速域名
    Retry: 3, // 请求重试次数，默认为2
    RetryDelay: 1000, // 请求重试间隔，单位毫秒，默认为1000
    // 设置用户代理
    Headers: {
      'User-Agent': 'CDN-Manager/1.0.0 (Tencent-COS-JS-SDK)'
    }
  };

  try {
    cosClientInstance = new COS(cosConfig);
    
    // 只在第一次初始化时打印日志
    if (!isInitialized) {
      console.log('腾讯云COS客户端初始化成功。');
      console.log('区域:', region);
      console.log('Bucket:', COS_CONFIG.bucket);
      console.log('协议:', cosConfig.Protocol);
      console.log('环境:', isDevelopment ? '开发环境' : '生产环境');
      if (cosConfig.Domain) {
        console.log('使用域名:', cosConfig.Domain);
        if (isDevelopment) {
          console.log('✓ 开发环境：使用本地代理避免CORS问题');
          console.log('✓ 开发环境：使用自定义签名处理确保认证正确');
        }
      }
      isInitialized = true;
    }
    
    return cosClientInstance;
  } catch (error) {
    console.error('腾讯云COS客户端初始化失败:', error);
    // 重置实例以便下次重试
    cosClientInstance = null;
    throw error;
  }
};

/**
 * 重置客户端实例（用于配置更新后重新初始化）
 */
export const resetCosClient = () => {
  cosClientInstance = null;
  isInitialized = false;
  console.log('腾讯云COS客户端实例已重置');
};

/**
 * 获取客户端初始化状态
 * @returns {boolean} 是否已初始化
 */
export const isClientInitialized = () => {
  return isInitialized && cosClientInstance !== null;
};

// 导出获取客户端实例的函数
export default getCosClient;

/**
 * JSDoc for COS_CONFIG to satisfy linter or type checker if needed.
 * @typedef {import('./tencentEnv').COS_CONFIG} COS_CONFIG
 */
