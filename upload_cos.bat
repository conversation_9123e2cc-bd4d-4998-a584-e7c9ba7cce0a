@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🔗 腾讯云COS JSON文件上传工具
echo 📡 使用与 Node.js 相同的 Vite 代理配置
echo 🔄 只上传MD5发生变化的JSON文件
echo ============================================================

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python
    echo 💡 请先安装Python 3.7或更高版本
    pause
    exit /b 1
)

REM 设置虚拟环境目录
set VENV_DIR=cos_upload_env

REM 检查虚拟环境是否存在
if not exist "%VENV_DIR%" (
    echo 📦 创建Python虚拟环境...
    python -m venv %VENV_DIR%
    if !errorlevel! neq 0 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
) else (
    echo ✅ 虚拟环境已存在
)

REM 激活虚拟环境
echo 🔌 激活虚拟环境...
call %VENV_DIR%\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ 激活虚拟环境失败
    pause
    exit /b 1
)

REM 检查腾讯云COS SDK
echo 📦 检查腾讯云COS SDK...
python -c "import qcloud_cos" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 腾讯云COS SDK 已安装
) else (
    echo ⚠️  未找到腾讯云COS SDK，正在安装...
    pip install cos-python-sdk-v5
    if !errorlevel! equ 0 (
        echo ✅ 腾讯云COS SDK 安装成功
    ) else (
        echo ❌ 腾讯云COS SDK 安装失败
        echo 💡 提示：如果遇到权限问题，请尝试以管理员身份运行
        pause
        exit /b 1
    )
)

REM 检查requests库
python -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装requests库...
    pip install requests
)

echo.

REM 检查命令行参数
set MODE=upload
set INIT_MODE=false
if "%1"=="--test" set MODE=test
if "%1"=="-t" set MODE=test
if "%1"=="--init" set INIT_MODE=true
if "%1"=="-i" set INIT_MODE=true

if "%MODE%"=="test" (
    echo 🧪 运行连接测试模式
    python test_cos_connection.py
    goto :end
)

if "%INIT_MODE%"=="true" (
    echo 🔄 MD5记录初始化模式
    python upload_json_files_proxy.py --init
) else (
    echo 🚀 正常上传模式（只上传变化的文件）
    python upload_json_files_proxy.py
)

:end
echo.
if %errorlevel% equ 0 (
    echo ✅ 操作完成
) else (
    echo ❌ 操作失败，错误代码: %errorlevel%
)

echo.
echo 💡 使用说明:
echo    upload_cos.bat           # 正常上传模式
echo    upload_cos.bat --init    # 初始化MD5记录
echo    upload_cos.bat --test    # 测试连接配置
echo.

pause
