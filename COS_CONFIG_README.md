# 腾讯云COS配置说明

## 概述

本文档说明如何配置 `upload_json_files_proxy.py` 脚本以使用腾讯云COS（对象存储）服务。该脚本已从华为云OBS迁移到腾讯云COS。

## 配置方法

### 方法1：直接修改代码（推荐用于开发环境）

编辑 `upload_json_files_proxy.py` 文件中的 `COSConfigProxy` 类：

```python
class COSConfigProxy:
    def __init__(self):
        # 腾讯云COS配置信息
        self.secret_id = 'AKID6p8DliXXXXXXXXXXXXXXXXXXXXXX'  # 替换为你的 SecretId
        self.secret_key = 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'  # 替换为你的 SecretKey
        self.bucket = 'satworld-resource-1234567890'  # 替换为你的 Bucket 名称
        self.region = 'ap-singapore'  # 替换为你的 COS 区域
        # ... 其他配置
```

### 方法2：使用环境变量（推荐用于生产环境）

设置以下环境变量：

```bash
export TENCENT_SECRET_ID="你的SecretId"
export TENCENT_SECRET_KEY="你的SecretKey"
export TENCENT_COS_BUCKET="你的Bucket名称"
export TENCENT_COS_REGION="你的COS区域"
export VITE_PROXY_SERVER="http://**************:3000/"
```

## 配置参数说明

### 必需参数

1. **SecretId** (`secret_id`)
   - 腾讯云API密钥ID
   - 格式：`AKID` 开头的字符串
   - 获取方式：腾讯云控制台 > 访问管理 > API密钥管理

2. **SecretKey** (`secret_key`)
   - 腾讯云API密钥Key
   - 格式：40位字符串
   - 获取方式：腾讯云控制台 > 访问管理 > API密钥管理

3. **Bucket** (`bucket`)
   - COS存储桶名称
   - 格式：`bucket-name-appid`（包含APPID）
   - 示例：`satworld-resource-1234567890`
   - 获取方式：腾讯云控制台 > 对象存储 > 存储桶列表

4. **Region** (`region`)
   - COS存储桶所在地域
   - 示例：`ap-singapore`、`ap-beijing`、`ap-shanghai`
   - 获取方式：腾讯云控制台 > 对象存储 > 存储桶列表

### 可选参数

5. **代理服务器** (`proxy_server`)
   - Vite开发服务器地址
   - 默认：`http://**************:3000/`
   - 用于开发环境的代理访问

## 从 cosService.js 获取配置

如果你已经有 Node.js 版本的腾讯云COS配置，可以从以下文件获取参数：

1. 查找 `utils/tencentEnv.js` 文件
2. 找到 `COS_CONFIG` 对象
3. 复制相应的配置值到 Python 脚本中

示例 `tencentEnv.js` 结构：
```javascript
export const COS_CONFIG = {
  secretId: 'AKID6p8DliXXXXXXXXXXXXXXXXXXXXXX',
  secretKey: 'XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
  bucket: 'satworld-resource-1234567890',
  region: 'ap-singapore'
};
```

## 安装依赖

确保安装了腾讯云COS Python SDK：

```bash
pip install cos-python-sdk-v5
```

## 使用说明

1. **配置完成后**，运行脚本：
   ```bash
   python upload_json_files_proxy.py
   ```

2. **初始化MD5记录**：
   ```bash
   python upload_json_files_proxy.py --init
   ```

3. **确保Vite服务器运行**：
   ```bash
   npm run dev
   ```

## 注意事项

1. **安全性**：不要将密钥提交到版本控制系统
2. **代理模式**：脚本通过Vite代理服务器访问COS，确保代理配置正确
3. **权限**：确保COS密钥具有存储桶的读写权限
4. **网络**：确保网络可以访问腾讯云COS服务

## 故障排除

### 常见错误

1. **认证失败**
   - 检查 SecretId 和 SecretKey 是否正确
   - 确认密钥是否有效且未过期

2. **存储桶不存在**
   - 检查 Bucket 名称是否包含 APPID
   - 确认存储桶在指定区域存在

3. **代理连接失败**
   - 确认 Vite 开发服务器正在运行
   - 检查代理服务器地址是否正确

4. **权限不足**
   - 确认密钥具有存储桶的读写权限
   - 检查存储桶的访问策略

### 调试建议

1. 启用详细日志输出
2. 检查网络连接
3. 验证配置参数
4. 测试代理服务器连接

## 更新历史

- **v1.0**: 从华为云OBS迁移到腾讯云COS
- 支持环境变量配置
- 保持与原有功能的兼容性
