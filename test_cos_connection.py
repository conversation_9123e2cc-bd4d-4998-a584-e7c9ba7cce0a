#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯云COS连接测试脚本
用于验证配置是否正确
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from qcloud_cos import CosConfig, CosS3Client
    from qcloud_cos.cos_exception import CosServiceError, CosClientError
except ImportError:
    print("❌ 错误: 请先安装腾讯云 COS SDK")
    print("运行: pip install cos-python-sdk-v5")
    exit(1)

from upload_json_files_proxy import COSConfigProxy

def test_cos_connection():
    """测试腾讯云COS连接"""
    print("🔗 腾讯云COS连接测试")
    print("=" * 50)
    
    # 初始化配置
    config = COSConfigProxy()
    
    print(f"📍 代理服务器: {config.proxy_server}")
    print(f"🎯 COS区域: {config.region}")
    print(f"📁 存储桶: {config.bucket}")
    print(f"🔑 SecretId: {config.secret_id[:10]}...")
    print()
    
    # 检查配置
    if config.secret_id.startswith('AKID6p8DliXXXXXXXXXXXXXXXXXXXXXX'):
        print("⚠️  警告: 请更新 SecretId 配置")
        return False
    
    if config.secret_key.startswith('XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX'):
        print("⚠️  警告: 请更新 SecretKey 配置")
        return False
    
    if '1234567890' in config.bucket:
        print("⚠️  警告: 请更新 Bucket 名称配置")
        return False
    
    try:
        # 配置代理
        import urllib.parse
        proxy_url = urllib.parse.urlparse(config.proxy_server)
        proxy_host = proxy_url.hostname
        proxy_port = proxy_url.port or 3000
        
        os.environ['http_proxy'] = f'http://{proxy_host}:{proxy_port}'
        os.environ['https_proxy'] = f'http://{proxy_host}:{proxy_port}'
        
        print(f"🔧 设置代理: {os.environ['http_proxy']}")
        
        # 初始化COS客户端
        cos_config = CosConfig(
            Region=config.region,
            SecretId=config.secret_id,
            SecretKey=config.secret_key,
            Scheme='https',
            Timeout=30
        )
        
        cos_client = CosS3Client(cos_config)
        
        print("🧪 测试连接...")
        
        # 测试1: 列出存储桶
        try:
            response = cos_client.list_buckets()
            print("✅ 成功连接到腾讯云COS")
            
            if 'Buckets' in response and response['Buckets']:
                print(f"🪣 找到 {len(response['Buckets'])} 个存储桶:")
                for bucket in response['Buckets'][:3]:  # 只显示前3个
                    print(f"   - {bucket['Name']} (创建时间: {bucket['CreationDate']})")
            else:
                print("📝 没有找到存储桶")
        except (CosServiceError, CosClientError) as e:
            print(f"❌ 列出存储桶失败: {e}")
            return False
        
        # 测试2: 检查目标存储桶
        try:
            response = cos_client.head_bucket(Bucket=config.bucket)
            print(f"✅ 目标存储桶 '{config.bucket}' 存在且可访问")
        except (CosServiceError, CosClientError) as e:
            print(f"❌ 无法访问目标存储桶 '{config.bucket}': {e}")
            return False
        
        # 测试3: 列出存储桶中的对象（限制数量）
        try:
            response = cos_client.list_objects(
                Bucket=config.bucket,
                Prefix='website_public/_config/pre-version/',
                MaxKeys=5
            )
            
            if 'Contents' in response:
                print(f"📄 在目标路径找到 {len(response['Contents'])} 个对象:")
                for obj in response['Contents']:
                    print(f"   - {obj['Key']} ({obj['Size']} 字节)")
            else:
                print("📝 目标路径下没有找到对象")
        except (CosServiceError, CosClientError) as e:
            print(f"⚠️  列出对象失败: {e}")
            # 这不是致命错误，可能是权限问题
        
        print()
        print("🎉 连接测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {str(e)}")
        return False
    finally:
        # 清理代理环境变量
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']

def check_vite_server():
    """检查Vite服务器是否运行"""
    import requests
    
    config = COSConfigProxy()
    try:
        print(f"🔍 检查Vite服务器: {config.proxy_server}")
        response = requests.get(config.proxy_server, timeout=5)
        print("✅ Vite开发服务器正在运行")
        return True
    except requests.exceptions.RequestException as e:
        print(f"❌ Vite开发服务器未运行: {e}")
        print("💡 请先启动开发服务器: npm run dev")
        return False

def main():
    """主函数"""
    print("🔗 腾讯云COS配置测试工具")
    print("=" * 50)
    
    # 检查Vite服务器
    if not check_vite_server():
        print("\n💡 提示: 请先启动Vite开发服务器，然后重新运行测试")
        return 1
    
    print()
    
    # 测试COS连接
    if test_cos_connection():
        print("🎉 所有测试通过！可以开始使用上传脚本。")
        return 0
    else:
        print("❌ 测试失败，请检查配置。")
        print("\n💡 配置帮助:")
        print("1. 检查 upload_json_files_proxy.py 中的 COSConfigProxy 类配置")
        print("2. 确保 SecretId、SecretKey、Bucket、Region 配置正确")
        print("3. 确认腾讯云密钥具有存储桶的读写权限")
        print("4. 参考 COS_CONFIG_README.md 文件获取详细配置说明")
        return 1

if __name__ == "__main__":
    exit(main())
